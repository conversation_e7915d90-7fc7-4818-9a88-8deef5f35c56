const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;


jscode = `var a = 0x25,b = 0b10001001,c = 0o123456,
d = "\x68\x65\x6c\x6c\x6f\x2c\x41\x53\x54",
e = "\u0068\u0065\u006c\u006c\u006f\u002c\u0041\u0053\u0054";
`

var ast = parse.parse(jscode);


const cyx = {
    'NumericLiteral'(path) {
        var {node} = path;
        // 只处理编码格式的数字（十六进制、八进制、二进制）
        if (node.extra && /^0[obx]/i.test(node.extra.raw)) {
            console.log('处理数字编码:', node.extra.raw, '→', node.value);
            node.extra = undefined;  // 清除编码信息，使用十进制显示
        }
    },
    'StringLiteral'(path) {
        var {node} = path;
        // 只处理转义编码的字符串
        if (node.extra && /\\[ux]/gi.test(node.extra.raw)) {
            console.log('处理字符串编码:', node.extra.raw, '→', node.value);
            node.extra = undefined;  // 清除转义信息，使用普通字符串显示
        }
    }
}

traverse(ast, cyx)
var {code} = generator(ast)
console.log(code)

