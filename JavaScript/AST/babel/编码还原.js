const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;


jscode = `var a = 0x25,b = 0b10001001,c = 0o123456,
d = "\x68\x65\x6c\x6c\x6f\x2c\x41\x53\x54",
e = "\u0068\u0065\u006c\u006c\u006f\u002c\u0041\u0053\u0054";
`

var ast = parse.parse(jscode);


const cyx = {
    'NumericLiteral|StringLiteral'(path){
        var {confident,value} = path.evaluate();
        console.log(confident,value);
        path.replaceWith(types.valueToNode(value));
    }
}

 traverse(ast,cyx)
var {code} = generator(ast)
console.log(code)

