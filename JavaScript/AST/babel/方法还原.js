const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

jscode = `  function xl (arg,arg1){
    return arg + arg1
}
var xx = xl('\u0068\u0065\u006c\u006c\u006f','\u002c\u0041\u0053\u0054') // --- > var xx = 'hello ast'
`

var ast = parse.parse(jscode);
// 判断类型是不是字面量
function isNodeLiteral(node) {
    if (Array.isArray(node)) {
        return node.every(ele => isNodeLiteral(ele));
    }
    if (types.isLiteral(node)) {
        if (node.value == null) {
            return false;
        }
        return true;
    }
    if (types.isBinaryExpression(node)) {
        return isNodeLiteral(node.left) && isNodeLiteral(node.right);
    }
    if (types.isUnaryExpression(node, {
        "operator": "-"
    }) || types.isUnaryExpression(node, {
        "operator": "+"
    })) {
        return isNodeLiteral(node.argument);
    }

    if (types.isObjectExpression(node)) {
        let {properties} = node;
        if (properties.length == 0) {
            return true;
        }

        return properties.every(property => isNodeLiteral(property));

    }
    if (types.isArrayExpression(node)) {
        let {elements} = node;
        if (elements.length == 0) {
            return true;
        }
        return elements.every(element => isNodeLiteral(element));
    }

    return false;
}



var transform = {
    functionDeclaration(path) {
        var {node, parentPath} = path;
        var body = node.body
        var id = node.id;
        if (!types.isReturnStatement(body.body[0])) {
            return;
        }
        // 获取绑定信息
        var binding = parentPath.scope.getBinding(id.name)
        //判断是否被引用
        if (!binding.referenced && !parentPath.isProgram()) {
            path.remove();
            return
        }
        var js_code = path.toString()
        console.log(typeof js_code)
        if (js_code.includes("try") || js_code.includes("random") || js_code.includes("Date")) {
            return
        }
        // 把代码加载到当前的js环境中，之前代码属于ast树是字符串，将字符串转换为可执行代码
        eval(js_code)
        var can_remove = true;
        for(const referPath of binding.referencePaths){
            var {parentPath,node} = referPath;
            if(!parentPath.isCallExpression({"callee":node})){
                can_remove = false;
                continue;
            }
            var arguments = parentPath.node.arguments;
            if(arguments == 0||)

    }
}