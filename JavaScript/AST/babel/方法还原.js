const parse = require('@babel/parser')
const traverse = require("@babel/traverse").default;
const types = require("@babel/types");
const generator = require("@babel/generator").default;

jscode = `  function xl (arg,arg1){
    return arg + arg1
}
var xx = xl('\u0068\u0065\u006c\u006c\u006f','\u002c\u0041\u0053\u0054') // --- > var xx = 'hello ast'
`

var ast = parse.parse(jscode);

var transform = {
    functionDeclaration(path){
        var {node,parentPath} = path;
        var body =node.body
        var id =node.id;
        if(!types.isReturnStatement(body.body[0])){
            return;
        }
        // 获取绑定信息
        var binding = parentPath.scope.getBinding(id.name)
        //判断是否被引用
        if(!binding.referenced && !parentPath.isProgram()){
            path.remove();
            return 
        }
    }
}